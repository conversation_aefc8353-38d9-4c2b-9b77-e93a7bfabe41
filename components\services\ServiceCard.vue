<template>
  <Card class="group hover:shadow-lg transition-all duration-300 overflow-hidden h-full flex flex-col">
    <!-- Service Image -->
    <div class="relative aspect-square overflow-hidden">
      <img
        v-if="service.imageUrl"
        :src="imageUrl"
        :alt="service.name"
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        @error="handleImageError"
      />
      <div
        v-else
        class="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center"
      >
        <Icon name="lucide:package" class="w-8 h-8 sm:w-12 sm:h-12 text-primary-600" />
      </div>

      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <span
          v-if="!service.isActive"
          class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full"
        >
          Inactive
        </span>
      </div>

      <!-- Action Buttons Overlay -->
      <div class="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div class="flex space-x-2">
          <Button
            @click.stop="$emit('edit', service)"
            size="sm"
            variant="secondary"
            class="bg-white text-neutral-900 hover:bg-neutral-100 shadow-lg"
          >
            <Icon name="lucide:edit" class="w-4 h-4" />
            <span class="sr-only">Edit service</span>
          </Button>
          <Button
            @click.stop="$emit('delete', service)"
            size="sm"
            variant="destructive"
            class="bg-red-600 text-white hover:bg-red-700 shadow-lg"
          >
            <Icon name="lucide:trash-2" class="w-4 h-4" />
            <span class="sr-only">Delete service</span>
          </Button>
        </div>
      </div>
    </div>

    <!-- Service Details -->
    <CardContent class="p-3 sm:p-4 flex-1 flex flex-col">
      <div class="space-y-2 flex-1">
        <div class="flex items-start justify-between gap-2">
          <h3 class="font-semibold text-neutral-900 text-base sm:text-lg leading-tight flex-1 min-w-0">
            {{ service.name }}
          </h3>
          <span class="text-base sm:text-lg font-bold text-primary-600 whitespace-nowrap">
            {{ formattedPrice }}
          </span>
        </div>

        <p class="text-neutral-600 text-xs sm:text-sm line-clamp-2 leading-relaxed">
          {{ service.description }}
        </p>

        <div class="flex items-center justify-between text-xs sm:text-sm text-neutral-500 pt-1">
          <div class="flex items-center space-x-1">
            <Icon name="lucide:clock" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ formattedDuration }}</span>
          </div>

          <div v-if="service.category" class="flex items-center space-x-1 min-w-0">
            <Icon name="lucide:tag" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ service.category }}</span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { Service } from '~/types/service.types'
import { formatDuration, formatCurrency } from '~/utils/services'

interface Props {
  service: Service
}

interface Emits {
  (e: 'edit', service: Service): void
  (e: 'delete', service: Service): void
  (e: 'book', service: Service): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// Memoized computed values for better performance
const imageUrl = computed(() => {
  if (!props.service.imageUrl) return ''

  // If it's already a full URL, return as is
  if (props.service.imageUrl.startsWith('http://') ||
      props.service.imageUrl.startsWith('https://') ||
      props.service.imageUrl.startsWith('data:')) {
    return props.service.imageUrl
  }

  // If it's a relative URL, prepend the API base URL
  const config = useRuntimeConfig()
  return `${config.public.apiBase}${props.service.imageUrl.startsWith('/') ? '' : '/'}${props.service.imageUrl}`
})

const formattedPrice = computed(() => formatCurrency(props.service.price))
const formattedDuration = computed(() => formatDuration(props.service.duration))

// Handle image loading errors
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  console.warn('Failed to load service image:', img.src)
  // Hide the image element so the fallback div shows
  img.style.display = 'none'
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
